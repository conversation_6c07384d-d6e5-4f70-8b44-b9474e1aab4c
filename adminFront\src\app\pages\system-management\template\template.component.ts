import { BaseComponent } from '../../components/base/baseComponent';
import { Component, OnInit, ViewChild, TemplateRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../components/shared.module';
import { NbDialogService } from '@nebular/theme';
import { AllowHelper } from 'src/app/shared/helper/allowHelper';
import { BreadcrumbComponent } from '../../components/breadcrumb/breadcrumb.component';
import { SpacePickerComponent, SpacePickerItem } from 'src/app/shared/components/space-picker/space-picker.component';
import { TemplateService, SpaceService } from 'src/services/api/services';
import { MessageService } from 'src/app/shared/services/message.service';
import { ValidationHelper } from 'src/app/shared/helper/validationHelper';
import { tap } from 'rxjs';
import {
  SaveTemplateArgs,
  GetTemplateDetailByIdArgs,
  TemplateDetailItem,
  GetSpaceListResponse
} from 'src/services/api/models';
import { EnumTemplateType, EnumTemplateTypeHelper } from 'src/app/shared/enum/enumTemplateType';

export interface TemplateItem {
  CTemplateId: number;
  CTemplateName: string;
  CTemplateType?: number;
  CCreateDt: string;
  CUpdateDt: string;
  CCreator?: string | null;
  CUpdator?: string | null;
  CStatus?: number;
  selected?: boolean;
}

export interface SpacePickListItem {
  CSpaceID: number;
  CPart: string;
  CLocation?: string | null;
  selected?: boolean;
}

// 僅用於模板明細空間顯示
export interface TemplateDetailSpaceItem {
  CReleateId: number;
  CPart: string;
  CLocation?: string | null;
}

// 項目模板選擇項目介面（擴展空間選擇項目，添加單價和單位）
export interface ItemPickListItem extends SpacePickListItem {
  CUnitPrice?: number;
  CUnit?: string;
}

@Component({
  selector: 'ngx-template',
  templateUrl: './template.component.html',
  styleUrls: ['./template.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    SharedModule,
    BreadcrumbComponent,
    SpacePickerComponent
  ],
})
export class TemplateComponent extends BaseComponent implements OnInit {
  Math = Math; // 讓模板可以使用 Math 函數
  EnumTemplateType = EnumTemplateType; // 讓模板可以使用枚舉
  EnumTemplateTypeHelper = EnumTemplateTypeHelper; // 讓模板可以使用枚舉助手

  @ViewChild('createModal', { static: false }) createModal!: TemplateRef<any>;
  @ViewChild('editModal', { static: false }) editModal!: TemplateRef<any>;
  @ViewChild('templateDetailModal', { static: false }) templateDetailModal!: TemplateRef<any>;

  constructor(
    protected override allow: AllowHelper,
    private dialogService: NbDialogService,
    private _templateService: TemplateService,
    private _spaceService: SpaceService,
    private message: MessageService,
    private valid: ValidationHelper
  ) {
    super(allow);
  }

  override pageFirst = 1;
  override pageSize = 10;
  override pageIndex = 1;
  override totalRecords = 0;

  // 模板相關屬性
  templateList: TemplateItem[] = [];
  templateDetail: SaveTemplateArgs = {};
  searchKeyword: string = '';
  searchStatus: number | null = null;
  searchTemplateType: number | null = null;

  // 空間選擇相關屬性
  availableSpaces: SpacePickListItem[] = [];
  selectedSpacesForTemplate: SpacePickListItem[] = [];
  spaceSearchKeyword: string = '';
  spaceSearchLocation: string = '';
  spacePageIndex = 1;
  spacePageSize = 10;
  spaceTotalRecords = 0;
  allSpacesSelected = false;

  // 項目選擇相關屬性（項目模板使用空間列表作為基礎，但添加單價和單位）
  availableItemsForTemplate: ItemPickListItem[] = [];
  selectedItemsForTemplate: ItemPickListItem[] = [];
  itemSearchKeyword: string = '';
  itemSearchLocation: string = '';
  itemPageIndex = 1;
  itemPageSize = 10;
  itemTotalRecords = 0;
  allItemsSelected = false;

  // 模板明細相關屬性
  selectedTemplateDetail: TemplateItem | null = null;
  templateDetailSpaces: TemplateDetailSpaceItem[] = [];
  isLoadingTemplateDetail = false;

  // 編輯模式相關屬性
  existingTemplateDetails: TemplateDetailItem[] = [];
  isLoadingExistingDetails = false;

  // 模態框模式控制
  isEditMode = false;

  override ngOnInit(): void {
    this.loadTemplateList();
    this.loadAvailableSpaces();
  }

  // 載入模板列表
  loadTemplateList(): void {
    const request = {
      CTemplateName: this.searchKeyword || null,
      CStatus: this.searchStatus,
      CTemplateType: this.searchTemplateType,
      PageIndex: this.pageIndex,
      PageSize: this.pageSize
    };

    this._templateService.apiTemplateGetTemplateListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.templateList = response.Entries?.map(item => ({
            CTemplateId: item.CTemplateId!,
            CTemplateName: item.CTemplateName!,
            CTemplateType: item.CTemplateType, // 新增模板類型
            CCreateDt: item.CCreateDt!,
            CUpdateDt: item.CUpdateDt!,
            CCreator: item.CCreator,
            CUpdator: item.CUpdator,
            CStatus: item.CStatus
          })) || [];
          this.totalRecords = response.TotalItems || 0;
        } else {
          this.message.showErrorMSG(response.Message || '載入模板列表失敗');
        }
      })
    ).subscribe();
  }

  // 載入可用空間列表
  loadAvailableSpaces(): void {
    const request = {
      CPart: this.spaceSearchKeyword || null,
      CLocation: this.spaceSearchLocation || null,
      CStatus: 1, // 只顯示啟用的空間
      PageIndex: this.spacePageIndex,
      PageSize: this.spacePageSize
    };

    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.availableSpaces = response.Entries?.map(item => ({
            CSpaceID: item.CSpaceID!,
            CPart: item.CPart!,
            CLocation: item.CLocation,
            selected: this.selectedSpacesForTemplate.some(s => s.CSpaceID === item.CSpaceID)
          })) || [];
          this.spaceTotalRecords = response.TotalItems || 0;
          this.updateAllSpacesSelectedState();
        }
      })
    ).subscribe();
  }

  // 搜尋功能
  onSearch(): void {
    this.pageIndex = 1;
    this.loadTemplateList();
  }

  onReset(): void {
    this.searchKeyword = '';
    this.searchStatus = null;
    this.pageIndex = 1;
    this.loadTemplateList();
  }

  // 載入項目模板可用項目（使用空間列表作為基礎）
  loadAvailableItemsForTemplate(): void {
    const request = {
      CPart: this.itemSearchKeyword || null,
      CLocation: this.itemSearchLocation || null,
      CStatus: 1, // 只顯示啟用的空間
      PageIndex: this.itemPageIndex,
      PageSize: this.itemPageSize
    };

    this._spaceService.apiSpaceGetSpaceListPost$Json({ body: request }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.availableItemsForTemplate = response.Entries?.map(item => ({
            CSpaceID: item.CSpaceID!,
            CPart: item.CPart!,
            CLocation: item.CLocation,
            selected: this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID),
            CUnitPrice: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnitPrice || 0,
            CUnit: this.selectedItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID)?.CUnit || '式'
          })) || [];
          this.itemTotalRecords = response.TotalItems || 0;
          this.updateAllItemsSelectedState();
        }
      })
    ).subscribe();
  }

  // 空間搜尋功能
  onSpaceSearch(): void {
    this.spacePageIndex = 1;
    this.loadAvailableSpaces();
  }

  onSpaceReset(): void {
    this.spaceSearchKeyword = '';
    this.spaceSearchLocation = '';
    this.spacePageIndex = 1;
    this.loadAvailableSpaces();
  }

  // 項目搜尋功能
  onItemSearch(): void {
    this.itemPageIndex = 1;
    this.loadAvailableItemsForTemplate();
  }

  onItemReset(): void {
    this.itemSearchKeyword = '';
    this.itemSearchLocation = '';
    this.itemPageIndex = 1;
    this.loadAvailableItemsForTemplate();
  }

  // 分頁功能
  pageChanged(page: number): void {
    this.pageIndex = page;
    this.loadTemplateList();
  }

  spacePageChanged(page: number): void {
    this.spacePageIndex = page;
    this.loadAvailableSpaces();
  }

  itemPageChanged(page: number): void {
    this.itemPageIndex = page;
    this.loadAvailableItemsForTemplate();
  }

  // 模態框操作
  openCreateModal(modal: TemplateRef<any>): void {
    this.isEditMode = false;
    this.templateDetail = {
      CStatus: 1,
      CTemplateType: EnumTemplateType.SpaceTemplate // 預設為空間模板
    };
    this.selectedSpacesForTemplate = [];
    this.selectedItemsForTemplate = [];
    this.loadAvailableSpaces();
    this.loadAvailableItemsForTemplate();
    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  openEditModal(modal: TemplateRef<any>, template: TemplateItem): void {
    this.isEditMode = true;
    this.templateDetail = {
      CTemplateId: template.CTemplateId,
      CTemplateName: template.CTemplateName,
      CTemplateType: template.CTemplateType || EnumTemplateType.SpaceTemplate,
      CStatus: template.CStatus || 1
    };

    // 清空之前的選擇
    this.selectedSpacesForTemplate = [];
    this.selectedItemsForTemplate = [];
    this.existingTemplateDetails = [];

    // 載入已選擇的明細
    this.loadExistingTemplateDetails(template.CTemplateId);

    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });
  }

  onClose(ref: any): void {
    ref.close();
  }

  // 計算模態框寬度
  get modalWidth(): string {
    return '800px';
  }

  onSubmit(ref: any): void {
    if (!this.validateTemplateForm()) {
      return;
    }

    if (this.templateDetail.CTemplateId) {
      this.updateTemplate(ref);
    } else {
      this.createTemplate(ref);
    }
  }

  // 驗證表單
  validateTemplateForm(): boolean {
    if (!this.templateDetail.CTemplateName?.trim()) {
      this.message.showErrorMSG('請輸入模板名稱');
      return false;
    }

    if (this.templateDetail.CTemplateType === undefined || this.templateDetail.CTemplateType === null) {
      this.message.showErrorMSG('請選擇模板類型');
      return false;
    }

    if (this.templateDetail.CStatus === undefined || this.templateDetail.CStatus === null) {
      this.message.showErrorMSG('請選擇模板狀態');
      return false;
    }

    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate && this.selectedSpacesForTemplate.length === 0) {
      this.message.showErrorMSG('空間模板請至少選擇一個空間');
      return false;
    }

    if (!this.templateDetail.CTemplateId && this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate && this.selectedItemsForTemplate.length === 0) {
      this.message.showErrorMSG('項目模板請至少選擇一個項目');
      return false;
    }

    // 驗證項目模板的單價和單位
    if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {
      for (const item of this.selectedItemsForTemplate) {
        // 檢核單價
        if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {
          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須大於0，請重新輸入`);
          return false;
        }

        // 檢核單價是否為有效數字
        if (isNaN(item.CUnitPrice)) {
          this.message.showErrorMSG(`項目「${item.CPart}」的單價必須為有效數字`);
          return false;
        }

        // 檢核單位
        if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {
          this.message.showErrorMSG(`項目「${item.CPart}」請輸入具體的單位（不能為空或預設值「式」）`);
          return false;
        }

        // 檢核單位長度
        if (item.CUnit.trim().length > 10) {
          this.message.showErrorMSG(`項目「${item.CPart}」的單位長度不能超過10個字元`);
          return false;
        }
      }
    }

    return true;
  }

  // 建立模板
  createTemplate(ref: any): void {
    let details: any[] = [];

    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {
      // 空間模板的詳細資料
      details = this.selectedSpacesForTemplate.map(space => ({
        CTemplateDetailId: null,
        CReleateId: space.CSpaceID,
        CPart: space.CPart,
        CLocation: space.CLocation
      }));
    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {
      // 項目模板的詳細資料，包含單價和單位
      details = this.selectedItemsForTemplate.map(item => ({
        CTemplateDetailId: null,
        CReleateId: item.CSpaceID,
        CPart: item.CPart,
        CLocation: item.CLocation,
        CUnitPrice: item.CUnitPrice,
        CUnit: item.CUnit
      }));
    }

    const templateData: SaveTemplateArgs = {
      CTemplateName: this.templateDetail.CTemplateName,
      CTemplateType: this.templateDetail.CTemplateType,
      CStatus: this.templateDetail.CStatus,
      Details: details.length > 0 ? details : undefined
    };

    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.message.showSucessMSG('建立模板成功');
          ref.close();
          this.loadTemplateList();
        } else {
          this.message.showErrorMSG(response.Message || '建立模板失敗');
        }
      })
    ).subscribe();
  }

  // 更新模板
  updateTemplate(ref: any): void {
    const templateData: SaveTemplateArgs = {
      CTemplateId: this.templateDetail.CTemplateId,
      CTemplateName: this.templateDetail.CTemplateName,
      CTemplateType: this.templateDetail.CTemplateType,
      CStatus: this.templateDetail.CStatus
    };

    this._templateService.apiTemplateSaveTemplatePost$Json({ body: templateData }).pipe(
      tap(response => {
        if (response.StatusCode === 0) {
          this.message.showSucessMSG('更新模板成功');
          ref.close();
          this.loadTemplateList();
        } else {
          this.message.showErrorMSG(response.Message || '更新模板失敗');
        }
      })
    ).subscribe();
  }


  // 刪除模板
  deleteTemplate(template: TemplateItem): void {
    if (confirm(`確定要刪除模板「${template.CTemplateName}」嗎？`)) {
      this._templateService.apiTemplateDeleteTemplatePost$Json({
        body: { CTemplateId: template.CTemplateId }
      }).pipe(
        tap(response => {
          if (response.StatusCode === 0) {
            this.message.showSucessMSG('刪除模板成功');
            this.loadTemplateList();
          } else {
            this.message.showErrorMSG(response.Message || '刪除模板失敗');
          }
        })
      ).subscribe();
    }
  }

  // 載入編輯模式下的已選擇明細
  loadExistingTemplateDetails(templateId: number): void {
    this.isLoadingExistingDetails = true;

    const request: GetTemplateDetailByIdArgs = {
      templateId: templateId
    };

    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(
      tap(response => {
        this.isLoadingExistingDetails = false;
        if (response.StatusCode === 0) {
          this.existingTemplateDetails = response.Entries || [];

          // 根據模板類型設置已選擇的項目
          if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {
            // 空間模板：設置已選擇的空間
            this.selectedSpacesForTemplate = this.existingTemplateDetails.map(item => ({
              CSpaceID: item.CReleateId!,
              CPart: item.CPart!,
              CLocation: item.CLocation,
              selected: true
            }));
          } else {
            // 項目模板：設置已選擇的項目（包含單價和單位）
            this.selectedItemsForTemplate = this.existingTemplateDetails.map(item => ({
              CSpaceID: item.CReleateId!,
              CPart: item.CPart!,
              CLocation: item.CLocation,
              CUnitPrice: item.CUnitPrice,
              CUnit: item.CUnit,
              selected: true
            }));
          }
        } else {
          this.message.showErrorMSG(response.Message || '載入模板明細失敗');
        }
      })
    ).subscribe();
  }

  // 查看模板明細
  viewTemplateDetail(template: TemplateItem, modal: TemplateRef<any>): void {
    this.selectedTemplateDetail = template;
    this.isLoadingTemplateDetail = true;
    this.templateDetailSpaces = [];

    this.dialogService.open(modal, {
      context: {},
      autoFocus: false
    });

    const request: GetTemplateDetailByIdArgs = {
      templateId: template.CTemplateId
    };

    this._templateService.apiTemplateGetTemplateDetailByIdPost$Json({ body: request }).pipe(
      tap(response => {
        this.isLoadingTemplateDetail = false;
        if (response.StatusCode === 0) {
          this.templateDetailSpaces = response.Entries?.map(item => ({
            CReleateId: item.CReleateId!,
            CPart: item.CPart!,
            CLocation: item.CLocation
          })) || [];
        } else {
          this.message.showErrorMSG(response.Message || '載入模板明細失敗');
        }
      })
    ).subscribe();
  }

  // 空間選擇相關方法
  toggleSpaceSelection(space: SpacePickListItem): void {
    space.selected = !space.selected;

    if (space.selected) {
      if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {
        this.selectedSpacesForTemplate.push({ ...space });
      }
    } else {
      this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);
    }

    this.updateAllSpacesSelectedState();
  }

  toggleAllSpaces(): void {
    this.allSpacesSelected = !this.allSpacesSelected;

    this.availableSpaces.forEach(space => {
      space.selected = this.allSpacesSelected;
      if (this.allSpacesSelected) {
        if (!this.selectedSpacesForTemplate.some(s => s.CSpaceID === space.CSpaceID)) {
          this.selectedSpacesForTemplate.push({ ...space });
        }
      } else {
        this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);
      }
    });
  }

  removeSelectedSpace(space: SpacePickListItem): void {
    this.selectedSpacesForTemplate = this.selectedSpacesForTemplate.filter(s => s.CSpaceID !== space.CSpaceID);

    const availableSpace = this.availableSpaces.find(s => s.CSpaceID === space.CSpaceID);
    if (availableSpace) {
      availableSpace.selected = false;
    }

    this.updateAllSpacesSelectedState();
  }

  updateAllSpacesSelectedState(): void {
    this.allSpacesSelected = this.availableSpaces.length > 0 &&
      this.availableSpaces.every(space => space.selected);
  }

  // 項目選擇相關方法
  toggleItemSelection(item: ItemPickListItem): void {
    item.selected = !item.selected;

    if (item.selected) {
      if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {
        // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）
        const newItem = {
          ...item,
          CUnitPrice: 0, // 設為0，強制用戶輸入
          CUnit: '' // 設為空，強制用戶輸入
        };
        this.selectedItemsForTemplate.push(newItem);
      }
    } else {
      this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);
    }

    this.updateAllItemsSelectedState();
  }

  toggleAllItems(): void {
    this.allItemsSelected = !this.allItemsSelected;

    this.availableItemsForTemplate.forEach(item => {
      item.selected = this.allItemsSelected;
      if (this.allItemsSelected) {
        if (!this.selectedItemsForTemplate.some(s => s.CSpaceID === item.CSpaceID)) {
          // 確保新選中的項目有預設值，但不是有效值（需要用戶填寫）
          const newItem = {
            ...item,
            CUnitPrice: 0, // 設為0，強制用戶輸入
            CUnit: '' // 設為空，強制用戶輸入
          };
          this.selectedItemsForTemplate.push(newItem);
        }
      } else {
        this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);
      }
    });
  }

  removeSelectedItem(item: ItemPickListItem): void {
    this.selectedItemsForTemplate = this.selectedItemsForTemplate.filter(s => s.CSpaceID !== item.CSpaceID);

    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);
    if (availableItem) {
      availableItem.selected = false;
    }

    this.updateAllItemsSelectedState();
  }

  updateAllItemsSelectedState(): void {
    this.allItemsSelected = this.availableItemsForTemplate.length > 0 &&
      this.availableItemsForTemplate.every(item => item.selected);
  }

  // 更新選中項目的單價和單位
  updateItemPrice(item: ItemPickListItem, price: number): void {
    // 確保價格為有效數字且大於0
    if (isNaN(price) || price < 0) {
      price = 0;
    }

    item.CUnitPrice = price;
    // 同步更新 availableItemsForTemplate 中對應項目的單價
    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);
    if (availableItem) {
      availableItem.CUnitPrice = price;
    }
  }

  // 處理單價變更事件
  onPriceChange(item: ItemPickListItem, value: any): void {
    // 確保值為數字類型
    const numericValue = typeof value === 'string' ? parseFloat(value) : value;
    item.CUnitPrice = isNaN(numericValue) ? 0 : numericValue;
  }

  updateItemUnit(item: ItemPickListItem, unit: string): void {
    // 清理單位字串
    unit = unit ? unit.trim() : '';

    item.CUnit = unit;
    // 同步更新 availableItemsForTemplate 中對應項目的單位
    const availableItem = this.availableItemsForTemplate.find(s => s.CSpaceID === item.CSpaceID);
    if (availableItem) {
      availableItem.CUnit = unit;
    }
  }

  // 處理空間選擇變更
  onSpaceSelectionChange(selectedSpaces: SpacePickerItem[]): void {
    this.selectedSpacesForTemplate = selectedSpaces.map(space => ({
      CSpaceID: space.CSpaceID,
      CPart: space.CPart,
      CLocation: space.CLocation,
      selected: true
    }));
  }

  // 處理項目選擇變更
  onItemSelectionChange(selectedItems: SpacePickerItem[]): void {
    this.selectedItemsForTemplate = selectedItems.map(item => ({
      CSpaceID: item.CSpaceID,
      CPart: item.CPart,
      CLocation: item.CLocation,
      selected: true,
      CUnitPrice: 0, // 預設為0，強制用戶輸入
      CUnit: '' // 預設為空，強制用戶輸入
    }));
  }

  // 統一的選擇變更處理方法
  onSelectionChange(selectedItems: SpacePickerItem[]): void {
    if (this.templateDetail.CTemplateType === EnumTemplateType.SpaceTemplate) {
      this.onSpaceSelectionChange(selectedItems);
    } else if (this.templateDetail.CTemplateType === EnumTemplateType.ItemTemplate) {
      this.onItemSelectionChange(selectedItems);
    }
  }

  // 檢查項目是否有效（用於UI顯示）
  isItemValid(item: ItemPickListItem): boolean {
    // 檢核單價
    if (item.CUnitPrice === undefined || item.CUnitPrice === null || item.CUnitPrice <= 0) {
      return false;
    }

    // 檢核單價是否為有效數字
    if (isNaN(item.CUnitPrice)) {
      return false;
    }

    // 檢核單位
    if (!item.CUnit || item.CUnit.trim() === '' || item.CUnit.trim() === '式') {
      return false;
    }

    // 檢核單位長度
    if (item.CUnit.trim().length > 10) {
      return false;
    }

    return true;
  }

  // 獲取已完成設定的項目數量
  getValidItemsCount(): number {
    return this.selectedItemsForTemplate.filter(item => this.isItemValid(item)).length;
  }
}
